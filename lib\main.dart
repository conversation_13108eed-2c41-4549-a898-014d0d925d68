import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'screens/home_screen.dart';
import 'services/rules_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final rulesService = RulesService();
  await rulesService.loadAllRules();

  runApp(MyApp(rulesService: rulesService));
}

class MyApp extends StatelessWidget {
  final RulesService rulesService;
  const MyApp({Key? key, required this.rulesService}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<RulesService>.value(value: rulesService),
      ],
      child: MaterialApp(
        title: 'Pension Calculator PK',
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en', 'US'), Locale('ur', 'PK')],
        theme: ThemeData(
          primarySwatch: Colors.indigo,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: const HomeScreen(),
      ),
    );
  }
}
