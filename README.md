# Pakistan Pension Calculator (Flutter)

A modern, reliable Flutter application to help Pakistani government employees (federal and provincial) accurately compute their pension, commutation, gratuity, family pension, and restoration timelines — citing real government rules and sources.

---

##  Purpose & Value

- Automates otherwise manual, error-prone pension calculations.
- Covers superannuation, retiring, death-in-service, and family pension cases.
- Supports multiple jurisdictions (Federal, KPK, Punjab, Sindh, Balochistan) with rules stored in easily updatable JSON files.
- Generates a PDF report with calculations, breakdown, and source citations.
- Includes localization (English and Urdu with RTL groundwork).

---

##  Official Rules & Formulas

### Core Pension Calculation

- **Gross Pension (monthly)** is calculated via:

Gross Pension = (Pensionable Emoluments × 7 × Qualifying Service Years) ÷ 300


- *Pensionable Emoluments*: Last basic pay or average of last 24 months (depending on jurisdiction policy) :contentReference[oaicite:0]{index=0}.
- *Qualifying Service* typically capped at 30 years :contentReference[oaicite:1]{index=1}.

### Commutation (Lump-Sum Option)

- Up to **35% of monthly pension** may be commuted per official rules :contentReference[oaicite:2]{index=2}.
- Lump sum is calculated using an **actuarial factor** (“years purchased”) based on age next birthday:

Lump Sum = (Gross × Commutation%) × 12 × YearsPurchased


(The actuarial table is embedded in `assets/tables/commutation_table.json`.)

### Restoration

- The commuted portion of pension is restored after the number of purchased years elapses (fraction years rounding as per official circulars) :contentReference[oaicite:3]{index=3}.

### Family Pension

Per Khyber Pakhtunkhwa rules:
- **Death before retirement**:  
- Gratuity = 25% of Gross Pension  
- Family Pension = 75% of Gross Pension per month :contentReference[oaicite:4]{index=4}.

- **Death after retirement**:  
- Family Pension = 100% of Net Pension (as drawn by deceased) per month :contentReference[oaicite:5]{index=5}.

### Pension Increases

- A **7% increase** in pension is applicable to retirees effective from 1 July 2025 onward :contentReference[oaicite:6]{index=6}.

---

##  Project Structure

pension_calculator_flutter/
├── assets/
│ ├── rules/
│ │ ├── federal.json
│ │ ├── kp.json
│ │ ├── punjab.json
│ │ ├── sindh.json
│ │ └── balochistan.json
│ ├── tables/
│ │ └── commutation_table.json
│ └── tests/
│ └── test_cases.csv
├── lib/
│ ├── main.dart
│ ├── models/ # Data models for rules
│ ├── services/ # Calculation logic & PDF generator
│ ├── screens/ # UI (Home, Calculator, Result, Settings)
│ ├── widgets/ # Reusable UI components
│ └── l10n/ # Localization files (ARB)
├── test/ # Unit testing scaffold
├── pubspec.yaml # Package and asset configuration
└── README.md # Project documentation


---

##  Running the App

1. Clone or unzip the project, open it in VS Code or your preferred editor.
2. Run:

   ```bash
   flutter pub get
   flutter run
   ```

3. The app should launch on your connected device or emulator.

---