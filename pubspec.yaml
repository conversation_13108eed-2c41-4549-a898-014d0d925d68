# Flutter dependencies and assets configuration
name: pakistan_pension_calculator
description: A full Flutter app to calculate government pension (federal & provinces) with rules-driven engine, PDF export, Urdu localization, unit tests and full jurisdiction rule files.
publish_to: 'none'
version: 0.1.0+1
environment:
  sdk: ">=2.18.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  intl: ^0.18.0
  flutter_localizations:
    sdk: flutter
  path_provider: ^2.0.12
  shared_preferences: ^2.1.1
  pdf: ^3.10.1
  printing: ^5.10.2
  csv: ^5.0.0
  collection: ^1.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  test: ^1.22.0

flutter:
  uses-material-design: true
  assets:
    - assets/rules/federal.json
    - assets/rules/kp.json
    - assets/rules/punjab.json
    - assets/rules/sindh.json
    - assets/rules/balochistan.json
    - assets/tables/commutation_table.json
    - assets/tests/test_cases.csv
  # localization
  assets_translations:
    - lib/l10n/intl_en.arb
    - lib/l10n/intl_ur.arb